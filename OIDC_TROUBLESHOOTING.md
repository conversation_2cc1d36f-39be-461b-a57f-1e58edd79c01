# OIDC Authentication Troubleshooting Guide

## 🚨 CRITICAL ISSUE IDENTIFIED: Non-Standard OIDC Provider

**Root Cause**: Your SSO provider (`https://sso.veasy.vn`) does not support the standard OpenID Connect discovery protocol. The discovery endpoint `/.well-known/openid_configuration` returns 404, which means this is likely a custom SSO solution that doesn't follow OIDC standards.

## Current Issue: `access_denied` Error

The `access_denied` error is occurring because the OIDC client library cannot discover the correct endpoints automatically.

## Error Pattern Analysis

Based on your logs:
```
GET /auth/callback?error=access_denied&state=[STATE_VALUE]&session_state=[SESSION_VALUE] 200 in XXXms
GET /?auth_error=true 200 in XXms
```

This indicates:
1. ✅ OIDC provider is redirecting back to your callback URL
2. ❌ Authentication is being denied by the OIDC provider
3. ✅ Error handling is working (redirecting to `/?auth_error=true`)

## Common Causes of `access_denied`

### 1. **Redirect URI Mismatch** (Most Common)
**Issue**: The redirect URI in your application doesn't exactly match what's registered in your OIDC provider.

**Your Current Config**:
- Authority: `https://sso.veasy.vn`
- Client ID: `veasy_web_client`
- Redirect URI: `http://localhost:3000/auth/callback`

**Check**:
- [ ] Is `http://localhost:3000/auth/callback` exactly registered in your OIDC provider?
- [ ] Are there any trailing slashes or case differences?
- [ ] Is the protocol (http vs https) correct?

### 2. **Client Configuration Issues**
**Check in your OIDC provider**:
- [ ] Is `veasy_web_client` configured as a **public client** (for PKCE)?
- [ ] Is the **Authorization Code flow** enabled?
- [ ] Is **PKCE** enabled/required?
- [ ] Are the correct **scopes** (`openid profile email`) allowed?

### 3. **User Access Issues**
- [ ] Does the user have permission to access this application?
- [ ] Are there any user group restrictions?
- [ ] Is the user account active and not locked?

### 4. **OIDC Provider Configuration**
- [ ] Is the client configured to allow the current domain?
- [ ] Are there any IP restrictions?
- [ ] Is the client enabled/active?

## Diagnostic Steps

### Step 1: Verify OIDC Provider Discovery
Test if your OIDC provider is accessible:

```bash
curl https://sso.veasy.vn/.well-known/openid_configuration
```

This should return JSON with endpoints like `authorization_endpoint`, `token_endpoint`, etc.

### Step 2: Check Redirect URI Registration
In your OIDC provider admin panel, verify that these URIs are registered for `veasy_web_client`:
- `http://localhost:3000/auth/callback`
- `http://localhost:3000/auth/silent-callback` (if using silent renewal)

### Step 3: Verify Client Settings
Ensure your client (`veasy_web_client`) has:
- **Client Type**: Public (for PKCE) or Confidential (if you have a client secret)
- **Grant Types**: Authorization Code
- **Response Types**: code
- **PKCE**: Enabled/Required
- **Scopes**: openid, profile, email

### Step 4: Test with Minimal Configuration
Temporarily disable some features to isolate the issue:

1. Disable silent renewal
2. Reduce scopes to just `openid`
3. Test with a different user account

## Enhanced Error Logging

I'll create an enhanced callback page that provides more detailed error information.

## Quick Fixes to Try

### Fix 1: Update OIDC Configuration
Try these configuration changes:

1. **Disable Silent Renewal** (temporarily):
   ```env
   # Add to your .env.local
   NEXT_PUBLIC_DISABLE_SILENT_RENEWAL=true
   ```

2. **Reduce Scopes** (temporarily):
   ```env
   # Add to your .env.local
   NEXT_PUBLIC_OIDC_SCOPE=openid
   ```

### Fix 2: Check for HTTPS Requirements
Some OIDC providers require HTTPS even in development. Try:
```env
NEXT_PUBLIC_OIDC_REDIRECT_URI=https://localhost:3000/auth/callback
```
(You'll need to run your dev server with HTTPS)

### Fix 3: Add Debug Logging
Enable detailed OIDC logging to see exactly what's happening.

## Next Steps

1. **Check OIDC Provider Logs**: Look at your `sso.veasy.vn` logs for more details about why access is denied
2. **Verify Client Registration**: Double-check all settings in your OIDC provider admin panel
3. **Test with Different User**: Try authenticating with a different user account
4. **Contact OIDC Admin**: If you're not the admin of `sso.veasy.vn`, contact them to verify the client configuration

## Common OIDC Provider Specific Issues

### Keycloak
- Check if the client has "Standard Flow Enabled"
- Verify "Valid Redirect URIs" exactly match
- Check if "Web Origins" includes your domain

### Auth0
- Verify "Allowed Callback URLs"
- Check "Application Type" is set correctly
- Ensure "Grant Types" includes "Authorization Code"

### Azure AD
- Check "Redirect URIs" in App Registration
- Verify "Supported account types"
- Ensure API permissions are granted

### IdentityServer
- Check "RedirectUris" in client configuration
- Verify "AllowedGrantTypes" includes "Code"
- Check "RequirePkce" setting matches your configuration

Would you like me to implement enhanced error logging to get more specific information about the failure?
