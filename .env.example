# OIDC Configuration
# Copy this file to .env.local and fill in your actual values

# REQUIRED: Your OIDC provider's issuer URL
# This is the base URL of your identity server
# Example: https://your-identity-server.com or https://auth.your-domain.com
NEXT_PUBLIC_OIDC_AUTHORITY=https://sso.veasy.vn

# REQUIRED: Client ID registered with your OIDC provider
# This should be the client ID you received when registering your application
# Example: chatbot-frontend-client
NEXT_PUBLIC_OIDC_CLIENT_ID=veasy_web_client

# REQUIRED: Redirect URI after successful authentication
# This should match your application's domain + "/auth/callback"
# Development: http://localhost:3000/auth/callback (or 3001 if 3000 is in use)
# Production: https://your-app.com/auth/callback
NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback

# REQUIRED: Redirect URI after logout
# This is where users are redirected after signing out
# Development: http://localhost:3000
# Production: https://your-app.com
NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000

# OPTIONAL: Silent redirect URI for token renewal
# This is used for automatic token refresh without user interaction
# Development: http://localhost:3000/auth/silent-callback
# Production: https://your-app.com/auth/silent-callback
NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback

# DISCOVERY WORKING! ✅
# Your OIDC provider supports automatic discovery
# The following endpoints were discovered automatically:
# - Authorization: https://sso.veasy.vn/connect/authorize
# - Token: https://sso.veasy.vn/connect/token
# - UserInfo: https://sso.veasy.vn/connect/userinfo
# - End Session: https://sso.veasy.vn/connect/endsession
# - JWKS: https://sso.veasy.vn/.well-known/openid-configuration/jwks

# No manual endpoint configuration needed!
NEXT_PUBLIC_OIDC_CLIENT_SECRET=secret
# OpenAI API Configuration (if you're using OpenAI)
# OPENAI_API_KEY=your-openai-api-key-here
