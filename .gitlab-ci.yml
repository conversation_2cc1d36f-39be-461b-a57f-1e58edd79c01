# GitLab CI/CD Best Practices Template
# This configuration follows industry best practices for Node.js/React applications

stages:
  - validate
  - test
  - build
  - security
  - deploy

variables:
  NODE_VERSION: "18"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

# Default settings for all jobs
default:
  image: node:${NODE_VERSION}-alpine
  cache:
    key: 
      files:
        - package-lock.json
    paths:
      - node_modules/
      - .npm/
  before_script:
    - npm ci --cache .npm --prefer-offline

# Validation stage
lint:
  stage: validate
  script:
    - npm run lint
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

type-check:
  stage: validate
  script:
    - npm run type-check
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Test stage
unit-tests:
  stage: test
  script:
    - npm run test:unit -- --coverage --watchAll=false
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# TODO: Add integration tests
# integration-tests:
#   stage: test
#   services:
#     - name: postgres:13
#       alias: postgres
#   variables:
#     POSTGRES_DB: test_db
#     POSTGRES_USER: test_user
#     POSTGRES_PASSWORD: test_pass
#   script:
#     - npm run test:integration
#   rules:
#     - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Build stage
build:
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - dist/
      - build/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Security stage
dependency-scanning:
  stage: security
  script:
    - npm audit --audit-level moderate
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# TODO: Add SAST (Static Application Security Testing)
# include:
#   - template: Security/SAST.gitlab-ci.yml

# TODO: Add container scanning if using Docker
# container-scanning:
#   stage: security
#   image: docker:stable
#   services:
#     - docker:dind
#   script:
#     - docker build -t $CI_PROJECT_NAME:$CI_COMMIT_SHA .
#     - echo "Add container scanning tool here"

# Deploy stages
deploy-staging:
  stage: deploy
  environment:
    name: staging
    url: https://staging.yourapp.com
  script:
    - echo "Add your staging deployment script here"
    # Example: Deploy to staging server/environment
    # - scp -r dist/ user@staging-server:/var/www/html/
    # - ssh user@staging-server "sudo systemctl restart nginx"
  artifacts:
    paths:
      - dist/
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  when: manual

deploy-production:
  stage: deploy
  environment:
    name: production
    url: https://yourapp.com
  script:
    - echo "Add your production deployment script here"
    # TODO: Implement production deployment
    # Consider using:
    # - Docker deployment to container registry
    # - Static site hosting (Netlify, Vercel, S3)
    # - Kubernetes deployment
    # - Traditional server deployment
  artifacts:
    paths:
      - dist/
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual

# TODO: Add additional jobs as needed:
# 
# performance-tests:
#   stage: test
#   script:
#     - npm run test:performance
#   artifacts:
#     reports:
#       performance: performance.json
# 
# e2e-tests:
#   stage: test
#   services:
#     - name: selenium/standalone-chrome:latest
#       alias: selenium
#   script:
#     - npm run test:e2e
# 
# docker-build:
#   stage: build
#   image: docker:stable
#   services:
#     - docker:dind
#   script:
#     - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
#     - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
# 
# release:
#   stage: deploy
#   script:
#     - npm run semantic-release
#   rules:
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Best Practices Applied:
# ✅ Proper stage organization
# ✅ Efficient caching strategy
# ✅ Conditional job execution with rules
# ✅ Artifact management with expiration
# ✅ Environment-specific deployments
# ✅ Security scanning integration
# ✅ Manual deployment gates for production
# ✅ Coverage reporting
# ✅ TODO comments for future enhancements