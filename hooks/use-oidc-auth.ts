'use client'

import { useState, useEffect, useCallback } from 'react'
import { User } from 'oidc-client-ts'
import {
  signIn,
  signOut,
  getUser,
  renewToken,
  removeUser,
  isAuthenticated,
  getAccessToken,
  getUserProfile
} from '@/lib/oidc-client'

export interface OidcAuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: Error | null
  accessToken: string | null
  profile: any
}

export interface OidcAuthActions {
  signIn: () => Promise<void>
  signOut: () => Promise<void>
  renewToken: () => Promise<void>
  clearError: () => void
}

export type UseOidcAuthReturn = OidcAuthState & OidcAuthActions

/**
 * Custom hook for OIDC authentication using oidc-client-ts directly
 * 
 * This provides the same interface as react-oidc-context but with more control
 * over the authentication flow and better error handling.
 */
export function useOidcAuth(): UseOidcAuthReturn {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Load user on mount
  useEffect(() => {
    let mounted = true

    const loadUser = async () => {
      try {
        console.log('🔄 Loading user session...')
        setIsLoading(true)
        setError(null)
        
        const currentUser = await getUser()
        
        if (mounted) {
          setUser(currentUser)
          console.log(currentUser ? '✅ User session loaded' : 'ℹ️ No user session')
        }
      } catch (err) {
        console.error('❌ Failed to load user:', err)
        if (mounted) {
          setError(err instanceof Error ? err : new Error('Failed to load user'))
        }
      } finally {
        if (mounted) {
          setIsLoading(false)
        }
      }
    }

    loadUser()

    return () => {
      mounted = false
    }
  }, [])

  // Sign in function
  const handleSignIn = useCallback(async () => {
    try {
      console.log('🔐 Initiating sign-in...')
      setError(null)
      setIsLoading(true)
      
      await signIn()
      // Note: This will redirect, so we won't reach the next lines
    } catch (err) {
      console.error('❌ Sign-in failed:', err)
      setError(err instanceof Error ? err : new Error('Sign-in failed'))
      setIsLoading(false)
    }
  }, [])

  // Sign out function
  const handleSignOut = useCallback(async () => {
    try {
      console.log('👋 Initiating sign-out...')
      setError(null)
      setIsLoading(true)
      
      // Clear local state first
      setUser(null)
      
      // Remove user from storage
      await removeUser()
      
      // Redirect to sign-out endpoint
      await signOut()
      // Note: This will redirect, so we won't reach the next lines
    } catch (err) {
      console.error('❌ Sign-out failed:', err)
      setError(err instanceof Error ? err : new Error('Sign-out failed'))
      setIsLoading(false)
    }
  }, [])

  // Token renewal function
  const handleRenewToken = useCallback(async () => {
    try {
      console.log('🔄 Renewing token...')
      setError(null)
      
      const renewedUser = await renewToken()
      setUser(renewedUser)
      
      console.log('✅ Token renewed successfully')
    } catch (err) {
      console.error('❌ Token renewal failed:', err)
      setError(err instanceof Error ? err : new Error('Token renewal failed'))
      
      // If renewal fails, clear the user
      setUser(null)
    }
  }, [])

  // Clear error function
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Derived state
  const authState: OidcAuthState = {
    user,
    isAuthenticated: isAuthenticated(user),
    isLoading,
    error,
    accessToken: getAccessToken(user),
    profile: getUserProfile(user)
  }

  const authActions: OidcAuthActions = {
    signIn: handleSignIn,
    signOut: handleSignOut,
    renewToken: handleRenewToken,
    clearError
  }

  return {
    ...authState,
    ...authActions
  }
}

/**
 * Hook specifically for handling the authentication callback
 * Use this in your callback page component
 */
export function useOidcCallback() {
  const [isProcessing, setIsProcessing] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let mounted = true

    const processCallback = async () => {
      try {
        console.log('🔄 Processing OIDC callback...')
        setIsProcessing(true)
        setError(null)

        // Import the callback handler dynamically to avoid SSR issues
        const { handleSignInCallback } = await import('@/lib/oidc-client')
        const callbackUser = await handleSignInCallback()

        if (mounted) {
          setUser(callbackUser)
          console.log('✅ Callback processed successfully')
        }
      } catch (err) {
        console.error('❌ Callback processing failed:', err)
        if (mounted) {
          setError(err instanceof Error ? err : new Error('Callback processing failed'))
        }
      } finally {
        if (mounted) {
          setIsProcessing(false)
        }
      }
    }

    // Only process callback if we're in the browser and have URL parameters
    if (typeof window !== 'undefined' && window.location.search) {
      processCallback()
    } else {
      setIsProcessing(false)
    }

    return () => {
      mounted = false
    }
  }, [])

  return {
    isProcessing,
    user,
    error,
    isAuthenticated: isAuthenticated(user)
  }
}
