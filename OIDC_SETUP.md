# OIDC Authentication Setup Guide

This document provides a comprehensive guide for setting up OpenID Connect (OIDC) authentication in your AI Chatbot application.

## Overview

The application has been enhanced with OIDC authentication integration, replacing the previous signup/login system. Users now authenticate through your SSO identity server using the OpenID Connect protocol.

## Key Changes Made

### 1. Removed Signup Functionality
- ✅ Removed signup buttons and forms from the UI
- ✅ Removed signup-related handlers and logic
- ✅ Updated authentication dialog to only show login option

### 2. Implemented OIDC Authentication
- ✅ Installed `react-oidc-context` and `oidc-client-ts` libraries
- ✅ Created OIDC configuration with placeholder values
- ✅ Implemented OIDC provider wrapper for the application
- ✅ Added authentication callback pages
- ✅ Updated authentication state management
- ✅ Added proper error handling

### 3. Updated Components
- ✅ `AuthDialog`: Now uses OIDC login instead of email/password forms
- ✅ `ChatHeader`: Removed signup button, kept login functionality
- ✅ Main page: Integrated with OIDC authentication state
- ✅ API routes: Updated to work with OIDC authentication

## Configuration Required

### 1. Environment Variables

Create a `.env.local` file in your project root with the following variables:

```env
# REQUIRED: Your OIDC provider's issuer URL
NEXT_PUBLIC_OIDC_AUTHORITY=https://your-identity-server.com

# REQUIRED: Client ID registered with your OIDC provider
NEXT_PUBLIC_OIDC_CLIENT_ID=your-client-id

# REQUIRED: Redirect URI after successful authentication
NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback

# REQUIRED: Redirect URI after logout
NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000

# OPTIONAL: Silent redirect URI for token renewal
NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback
```

### 2. OIDC Provider Registration

When registering your application with your OIDC provider, configure:

#### Redirect URIs:
- `http://localhost:3000/auth/callback` (development)
- `https://your-app.com/auth/callback` (production)
- `http://localhost:3000/auth/silent-callback` (development - for silent renewal)
- `https://your-app.com/auth/silent-callback` (production - for silent renewal)

#### Post-logout Redirect URIs:
- `http://localhost:3000` (development)
- `https://your-app.com` (production)

#### Required Settings:
- ✅ Enable Authorization Code flow with PKCE
- ✅ Configure scopes: `openid profile email`
- ✅ Set appropriate token lifetimes
- ✅ Enable refresh tokens (recommended)

## File Structure

```
├── lib/
│   └── oidc-config.ts          # OIDC configuration with detailed comments
├── components/
│   ├── oidc-provider.tsx       # OIDC authentication provider
│   ├── auth-dialog.tsx         # Updated login dialog (OIDC only)
│   └── chat-header.tsx         # Updated header (no signup button)
├── app/
│   ├── auth/
│   │   ├── callback/
│   │   │   └── page.tsx        # Authentication callback handler
│   │   └── silent-callback/
│   │       └── page.tsx        # Silent token renewal handler
│   ├── layout.tsx              # Updated with OIDC provider
│   ├── page.tsx                # Updated with OIDC integration
│   └── api/
│       └── chat/
│           └── route.ts        # Updated API route
├── .env.example                # Environment variables template
└── OIDC_SETUP.md              # This setup guide
```

## Testing the Implementation

### 1. Build Verification
```bash
npm run build
```
✅ Build completed successfully without errors

### 2. Development Server
```bash
npm run dev
```
✅ Server starts on http://localhost:3000
✅ Application loads at http://localhost:3000 (returns HTTP 200)

### 3. Authentication Flow Testing

1. **Without Configuration**:
   - ✅ Application loads successfully
   - ✅ Shows warning: "OIDC not configured. Please set NEXT_PUBLIC_OIDC_AUTHORITY and NEXT_PUBLIC_OIDC_CLIENT_ID environment variables."
   - ✅ Login button appears but shows warning when clicked (expected behavior)
   - ✅ Application functions in limited mode without authentication

2. **With Configuration**:
   - Click "Sign In" button
   - Redirects to your OIDC provider
   - After authentication, redirects back to `/auth/callback`
   - User is signed in and can access full features

## Features

### Authentication Features
- ✅ SSO integration with your identity server
- ✅ Automatic token renewal (silent refresh)
- ✅ Proper logout handling
- ✅ Error handling for authentication failures
- ✅ Loading states during authentication

### Application Features
- ✅ Chat functionality for authenticated users
- ✅ Limited functionality for unauthenticated users
- ✅ Chat history (requires authentication)
- ✅ Protected routes and features

## Troubleshooting

### Common Issues

1. **"Authority not configured" error**
   - Ensure `NEXT_PUBLIC_OIDC_AUTHORITY` is set in `.env.local`

2. **"Invalid redirect URI" error**
   - Verify redirect URIs are registered in your OIDC provider
   - Check that URLs match exactly (including protocol and port)

3. **Silent renewal failures**
   - Ensure silent redirect URI is configured
   - Check that your OIDC provider supports silent renewal

4. **CORS errors**
   - Configure CORS settings in your OIDC provider
   - Ensure your domain is allowed

### Debug Mode

For development debugging, check browser console for OIDC-related logs. The OIDC provider includes detailed logging for authentication events.

## Security Considerations

- ✅ Uses Authorization Code flow with PKCE (most secure)
- ✅ Tokens are managed securely by the OIDC client
- ✅ No sensitive credentials stored in frontend code
- ✅ Automatic token renewal prevents session expiration
- ✅ Proper logout clears all authentication state

## Next Steps

1. **Configure your OIDC provider** with the settings above
2. **Update environment variables** with your actual values
3. **Test the authentication flow** end-to-end
4. **Deploy to production** with production URLs
5. **Monitor authentication logs** for any issues

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your OIDC provider configuration
3. Ensure environment variables are correctly set
4. Test with a simple OIDC client first to verify provider setup
