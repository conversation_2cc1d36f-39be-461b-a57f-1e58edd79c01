'use client'

import { useEffect, useState } from 'react'
import { useOidcAuth } from '@/components/oidc-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RefreshCw, CheckCircle, XCircle, Clock } from 'lucide-react'

/**
 * Token Exchange Debug Page
 * 
 * This page helps debug token exchange issues
 * Access it at: http://localhost:3000/token-debug
 */
export default function TokenDebugPage() {
  const auth = useOidcAuth()
  const [logs, setLogs] = useState<string[]>([])
  const [tokenInfo, setTokenInfo] = useState<any>(null)

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(`[Token Debug] ${message}`)
  }

  useEffect(() => {
    addLog('Token debug page loaded')
    addLog(`Auth state: isAuthenticated=${auth.isAuthenticated}, isLoading=${auth.isLoading}, hasError=${!!auth.error}`)
    
    if (auth.user) {
      const tokenData = {
        profile: auth.user.profile,
        access_token: auth.user.access_token ? {
          present: true,
          expires_at: auth.user.expires_at,
          expires_in: auth.user.expires_in,
          token_type: auth.user.token_type,
          scope: auth.user.scope
        } : null,
        id_token: auth.user.id_token ? {
          present: true,
          // Parse ID token payload (just for debugging)
          payload: auth.user.id_token ? JSON.parse(atob(auth.user.id_token.split('.')[1])) : null
        } : null,
        refresh_token: auth.user.refresh_token ? { present: true } : null
      }
      setTokenInfo(tokenData)
      addLog('User tokens loaded successfully')
    }

    if (auth.error) {
      addLog(`Authentication error: ${auth.error.message}`)
    }
  }, [auth.isAuthenticated, auth.isLoading, auth.error, auth.user])

  const testTokenRefresh = async () => {
    addLog('Testing token refresh...')
    try {
      // This will trigger a token refresh if possible
      await auth.signinSilent()
      addLog('Token refresh successful')
    } catch (error) {
      addLog(`Token refresh failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  const getStatusIcon = (condition: boolean) => {
    return condition ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-purple-900 mb-2">
            Token Exchange Debug
          </h1>
          <p className="text-purple-700">
            Debug token exchange and authentication state
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/'}
            className="w-full"
          >
            Back to Chat
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/oidc-diagnostics'}
            className="w-full"
          >
            Full Diagnostics
          </Button>
        </div>

        {/* Authentication Status */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                {getStatusIcon(auth.isAuthenticated)}
                <span className={auth.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                  {auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {auth.isLoading ? (
                  <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-gray-400" />
                )}
                <span className={auth.isLoading ? 'text-blue-600' : 'text-gray-600'}>
                  {auth.isLoading ? 'Loading...' : 'Ready'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(!auth.error)}
                <span className={auth.error ? 'text-red-600' : 'text-green-600'}>
                  {auth.error ? `Error: ${auth.error.message}` : 'No Errors'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Token Information */}
        {tokenInfo && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Token Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-green-600 mb-2">User Profile</h3>
                  <pre className="bg-green-50 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(tokenInfo.profile, null, 2)}
                  </pre>
                </div>
                
                {tokenInfo.access_token && (
                  <div>
                    <h3 className="font-semibold text-blue-600 mb-2">Access Token</h3>
                    <pre className="bg-blue-50 p-3 rounded text-sm overflow-auto">
                      {JSON.stringify(tokenInfo.access_token, null, 2)}
                    </pre>
                  </div>
                )}
                
                {tokenInfo.id_token && (
                  <div>
                    <h3 className="font-semibold text-purple-600 mb-2">ID Token Payload</h3>
                    <pre className="bg-purple-50 p-3 rounded text-sm overflow-auto">
                      {JSON.stringify(tokenInfo.id_token.payload, null, 2)}
                    </pre>
                  </div>
                )}
                
                {tokenInfo.refresh_token && (
                  <div>
                    <h3 className="font-semibold text-orange-600 mb-2">Refresh Token</h3>
                    <div className="bg-orange-50 p-3 rounded text-sm">
                      Refresh token is present
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Debug Actions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Debug Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button onClick={testTokenRefresh} disabled={!auth.isAuthenticated}>
                Test Token Refresh
              </Button>
              <Button variant="outline" onClick={clearLogs}>
                Clear Logs
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/auth/callback'}
              >
                Go to Callback Page
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Debug Logs */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
