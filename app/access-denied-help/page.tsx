'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { analyzeAccessDeniedError, generateClientConfigChecklist } from '@/lib/oidc-diagnostics'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, CheckCircle, Copy, ExternalLink } from 'lucide-react'

/**
 * Access Denied Troubleshooting Page
 * 
 * This page provides specific guidance for resolving access_denied errors
 * Access it at: http://localhost:3000/access-denied-help
 */
export default function AccessDeniedHelpPage() {
  const searchParams = useSearchParams()
  const [analysis, setAnalysis] = useState<any[]>([])
  const [checklist, setChecklist] = useState<any>(null)
  const [copiedItem, setCopiedItem] = useState<string | null>(null)

  useEffect(() => {
    // Analyze any callback URL from query params
    const callbackUrl = searchParams.get('callback_url')
    if (callbackUrl) {
      const results = analyzeAccessDeniedError(callbackUrl)
      setAnalysis(results)
    }
    
    // Generate client configuration checklist
    const checklistResult = generateClientConfigChecklist()
    setChecklist(checklistResult)
  }, [searchParams])

  const copyToClipboard = async (text: string, item: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedItem(item)
      setTimeout(() => setCopiedItem(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const currentConfig = {
    authority: process.env.NEXT_PUBLIC_OIDC_AUTHORITY,
    client_id: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID,
    redirect_uri: process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI,
    post_logout_redirect_uri: process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI,
    scope: process.env.NEXT_PUBLIC_OIDC_SCOPE || 'openid profile email'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-red-900 mb-2 flex items-center gap-2">
            <AlertCircle className="h-8 w-8" />
            Access Denied Troubleshooting
          </h1>
          <p className="text-red-700">
            Comprehensive guide to resolve OIDC access_denied errors
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/'}
            className="w-full"
          >
            Back to Chat
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/oidc-diagnostics'}
            className="w-full"
          >
            Full Diagnostics
          </Button>
        </div>

        {/* Error Analysis */}
        {analysis.length > 0 && (
          <Card className="mb-6 border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">Error Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {analysis.map((result, index) => (
                <div key={index} className="space-y-4">
                  <div className="p-4 bg-red-50 rounded-lg">
                    <h3 className="font-semibold text-red-800 mb-2">{result.message}</h3>
                    {result.details?.analysis && (
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-red-700 mb-2">Most Likely Causes:</h4>
                          <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                            {result.details.analysis.likely_causes.map((cause: string, i: number) => (
                              <li key={i}>{cause}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Client Configuration Checklist */}
        {checklist && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-blue-600">SSO Provider Configuration Checklist</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-3">Required Settings in Your SSO Admin Panel</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(checklist.details.required_settings).map(([key, value]) => (
                      <div key={key} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-sm text-gray-700">{key}:</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(String(value), key)}
                            className="h-6 px-2"
                          >
                            {copiedItem === key ? (
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <div className="text-sm bg-white p-2 rounded border font-mono">
                          {Array.isArray(value) ? value.join(', ') : String(value)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <h3 className="font-semibold text-green-800 mb-3">Verification Steps</h3>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-green-700">
                    {checklist.details.verification_steps.map((step: string, index: number) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Current Configuration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Your Current Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(currentConfig).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm text-gray-700">{key.toUpperCase()}:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(String(value), key)}
                      className="h-6 px-2"
                    >
                      {copiedItem === key ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                  <div className="text-sm bg-gray-100 p-2 rounded border font-mono">
                    {value || '(not set)'}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Common Solutions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-orange-600">Common Solutions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border-l-4 border-orange-400 bg-orange-50">
                <h3 className="font-semibold text-orange-800 mb-2">1. Redirect URI Mismatch (Most Common)</h3>
                <p className="text-sm text-orange-700 mb-2">
                  The redirect URI in your SSO provider must match exactly:
                </p>
                <div className="bg-white p-2 rounded font-mono text-sm border">
                  {currentConfig.redirect_uri}
                </div>
                <p className="text-xs text-orange-600 mt-1">
                  Check for trailing slashes, case sensitivity, and protocol (http vs https)
                </p>
              </div>

              <div className="p-4 border-l-4 border-blue-400 bg-blue-50">
                <h3 className="font-semibold text-blue-800 mb-2">2. Client Type Configuration</h3>
                <p className="text-sm text-blue-700 mb-2">
                  For PKCE authentication, your client must be configured as:
                </p>
                <ul className="text-sm text-blue-600 list-disc list-inside">
                  <li>Client Type: <strong>Public</strong> or <strong>SPA</strong></li>
                  <li>Grant Types: <strong>authorization_code</strong></li>
                  <li>PKCE: <strong>Required</strong> or <strong>Enabled</strong></li>
                  <li>Client Secret: <strong>Not required</strong></li>
                </ul>
              </div>

              <div className="p-4 border-l-4 border-purple-400 bg-purple-50">
                <h3 className="font-semibold text-purple-800 mb-2">3. User Permissions</h3>
                <p className="text-sm text-purple-700">
                  Verify that your user account has permission to access the client application.
                  Check user groups, roles, or client-specific access controls in your SSO provider.
                </p>
              </div>

              <div className="p-4 border-l-4 border-green-400 bg-green-50">
                <h3 className="font-semibold text-green-800 mb-2">4. Scope Configuration</h3>
                <p className="text-sm text-green-700 mb-2">
                  Ensure the requested scopes are allowed for your client:
                </p>
                <div className="bg-white p-2 rounded font-mono text-sm border">
                  {currentConfig.scope}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-indigo-600">Quick Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-indigo-50 rounded-lg">
                <h3 className="font-semibold text-indigo-800 mb-2">Test with Minimal Scope</h3>
                <p className="text-sm text-indigo-700 mb-3">
                  Add this to your .env.local file to test with minimal permissions:
                </p>
                <div className="bg-white p-3 rounded border font-mono text-sm">
                  NEXT_PUBLIC_OIDC_SCOPE=openid
                </div>
                <p className="text-xs text-indigo-600 mt-2">
                  Restart your dev server after making this change
                </p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-800 mb-2">Check SSO Provider Logs</h3>
                <p className="text-sm text-gray-700">
                  Look for detailed error messages in your SSO provider's admin panel or logs.
                  These often contain specific information about why access was denied.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
