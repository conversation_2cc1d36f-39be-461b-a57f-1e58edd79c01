'use client'

import { useEffect, useState } from 'react'
import { generateDiagnosticReport, type OidcDiagnosticReport } from '@/lib/oidc-diagnostics'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

/**
 * OIDC Diagnostics Page
 * 
 * This page helps diagnose OIDC configuration issues.
 * Access it at: http://localhost:3000/oidc-diagnostics
 */
export default function OidcDiagnosticsPage() {
  const [report, setReport] = useState<OidcDiagnosticReport | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const runDiagnostics = async () => {
    setLoading(true)
    setError(null)
    try {
      const diagnosticReport = await generateDiagnosticReport()
      setReport(diagnosticReport)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Run diagnostics on page load
    runDiagnostics()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return '✅'
      case 'warning': return '⚠️'
      case 'fail': return '❌'
      default: return '❓'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'fail': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            OIDC Configuration Diagnostics
          </h1>
          <p className="text-gray-600">
            This tool helps diagnose issues with your OpenID Connect configuration.
          </p>
        </div>

        <div className="mb-6">
          <Button 
            onClick={runDiagnostics} 
            disabled={loading}
            className="mr-4"
          >
            {loading ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/'}
          >
            Back to Chat
          </Button>
        </div>

        {error && (
          <Card className="mb-6 border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-600">{error}</p>
            </CardContent>
          </Card>
        )}

        {report && (
          <div className="space-y-6">
            {/* Overall Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(report.overall)}
                  <span className={getStatusColor(report.overall)}>
                    Overall Status: {report.overall.toUpperCase()}
                  </span>
                </CardTitle>
              </CardHeader>
            </Card>

            {/* Individual Checks */}
            <Card>
              <CardHeader>
                <CardTitle>Configuration Checks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.results.map((result, index) => (
                    <div key={index} className="border-l-4 border-gray-200 pl-4">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-lg">{getStatusIcon(result.status)}</span>
                        <h3 className="font-semibold">{result.check}</h3>
                      </div>
                      <p className={`mb-2 ${getStatusColor(result.status)}`}>
                        {result.message}
                      </p>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                            Show Details
                          </summary>
                          <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recommendations */}
            {report.recommendations.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Recommendations</CardTitle>
                </CardHeader>
                <CardContent>
                  <ol className="list-decimal list-inside space-y-2">
                    {report.recommendations.map((recommendation, index) => (
                      <li key={index} className="text-gray-700">
                        {recommendation}
                      </li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            )}

            {/* Current Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Current Environment Variables</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 font-mono text-sm">
                  <div>
                    <strong>NEXT_PUBLIC_OIDC_AUTHORITY:</strong>{' '}
                    <span className="text-blue-600">
                      {process.env.NEXT_PUBLIC_OIDC_AUTHORITY || '(not set)'}
                    </span>
                  </div>
                  <div>
                    <strong>NEXT_PUBLIC_OIDC_CLIENT_ID:</strong>{' '}
                    <span className="text-blue-600">
                      {process.env.NEXT_PUBLIC_OIDC_CLIENT_ID || '(not set)'}
                    </span>
                  </div>
                  <div>
                    <strong>NEXT_PUBLIC_OIDC_REDIRECT_URI:</strong>{' '}
                    <span className="text-blue-600">
                      {process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI || '(not set)'}
                    </span>
                  </div>
                  <div>
                    <strong>NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI:</strong>{' '}
                    <span className="text-blue-600">
                      {process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI || '(not set)'}
                    </span>
                  </div>
                  <div>
                    <strong>NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI:</strong>{' '}
                    <span className="text-blue-600">
                      {process.env.NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI || '(not set)'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Troubleshooting Tips */}
            <Card>
              <CardHeader>
                <CardTitle>Common Solutions for access_denied Error</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-red-600">1. Redirect URI Mismatch</h4>
                    <p className="text-sm text-gray-600">
                      Ensure <code>http://localhost:3000/auth/callback</code> is exactly registered in your OIDC provider.
                      Check for trailing slashes, case sensitivity, and protocol (http vs https).
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-yellow-600">2. Client Configuration</h4>
                    <p className="text-sm text-gray-600">
                      Verify your client is configured as a public client with PKCE enabled,
                      Authorization Code flow allowed, and correct scopes (openid, profile, email).
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-blue-600">3. User Permissions</h4>
                    <p className="text-sm text-gray-600">
                      Check if the user has permission to access this application and
                      if there are any group restrictions or account issues.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
