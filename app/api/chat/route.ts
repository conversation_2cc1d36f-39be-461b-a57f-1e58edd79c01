import { openai } from "@ai-sdk/openai"
import { streamText } from "ai"

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  const { messages, isLoggedIn } = await req.json()

  // If user is not logged in, return a login prompt
  if (!isLoggedIn) {
    return new Response(
      JSON.stringify({
        message:
          "Please sign in to continue chatting. Click the 'Sign In' button to authenticate with your SSO account and access the full AI assistant features including chat history.",
      }),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }

  const result = streamText({
    model: openai("gpt-4o"),
    messages,
    system: "You are a helpful AI assistant. Be concise and friendly in your responses.",
  })

  return result.toDataStreamResponse()
}
