'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useOidcCallback } from '@/hooks/use-oidc-auth'

/**
 * OIDC Authentication Callback Page
 *
 * This page handles the redirect from your OIDC provider after authentication.
 * It processes the authorization code and completes the authentication flow using
 * direct oidc-client-ts implementation for better control and debugging.
 *
 * The URL for this page should be registered as a redirect URI in your OIDC provider:
 * - Development: http://localhost:3000/auth/callback
 * - Production: https://your-app.com/auth/callback
 */
export default function AuthCallbackPage() {
  const { isProcessing, user, error, isAuthenticated } = useOidcCallback()
  const router = useRouter()
  const [debugInfo, setDebugInfo] = useState<string>('')

  useEffect(() => {
    // Capture URL parameters for debugging
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const error = urlParams.get('error')
      const code = urlParams.get('code')
      const state = urlParams.get('state')

      const debugData = {
        url: window.location.href,
        error: error,
        error_description: urlParams.get('error_description'),
        code: code ? '[PRESENT]' : null,
        state: state,
        session_state: urlParams.get('session_state'),
        scope: urlParams.get('scope'),
        iss: urlParams.get('iss'),
        callbackState: {
          isProcessing,
          isAuthenticated,
          error: error?.message || null,
          user: user ? {
            sub: user.profile?.sub,
            name: user.profile?.name,
            email: user.profile?.email
          } : null
        },
        timestamp: new Date().toISOString()
      }

      const debugString = JSON.stringify(debugData, null, 2)
      setDebugInfo(debugString)
      console.log('OIDC Callback Debug Info:', debugData)
    }

    // Handle callback completion
    if (!isProcessing) {
      if (error) {
        console.error('Callback processing failed:', error)
        if (error.message?.includes('access_denied')) {
          const callbackUrl = encodeURIComponent(window.location.href)
          router.replace(`/access-denied-help?callback_url=${callbackUrl}`)
        } else {
          router.replace(`/?auth_error=true&phase=token_exchange&error=${encodeURIComponent(error.message)}`)
        }
      } else if (isAuthenticated && user) {
        console.log('✅ Authentication completed successfully!', {
          user: user.profile,
          accessToken: user.access_token ? '[PRESENT]' : null,
          idToken: user.id_token ? '[PRESENT]' : null
        })
        router.replace('/')
      } else {
        console.error('❌ Callback completed but user not authenticated')
        router.replace('/?auth_error=true&phase=callback_completion')
      }
    }
  }, [isProcessing, isAuthenticated, user, error, router])

  // Determine the current phase of authentication (client-side only)
  const [hasCode, setHasCode] = useState(false)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      setHasCode(!!urlParams.get('code'))
      setHasError(!!urlParams.get('error'))
    }
  }, [])

  let phase = 'Processing'
  let description = 'Please wait while we sign you in.'

  if (hasError || error) {
    phase = 'Error Detected'
    description = 'An authentication error occurred.'
  } else if (hasCode && !isAuthenticated && isProcessing) {
    phase = 'Exchanging Token'
    description = 'Authorization code received. Exchanging for access token...'
  } else if (isProcessing) {
    phase = 'Completing Authentication'
    description = 'Finalizing your login...'
  }

  // Show loading state while processing authentication
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center max-w-2xl mx-auto p-6">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {phase}
        </h2>
        <p className="text-gray-600 mb-6">
          {description}
        </p>

        {/* Status indicators */}
        <div className="space-y-2 mb-6">
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${hasCode ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className={hasCode ? 'text-green-600' : 'text-gray-500'}>
              Authorization Code {hasCode ? 'Received' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isProcessing ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'}`}></div>
            <span className={isProcessing ? 'text-blue-600' : 'text-gray-500'}>
              Token Exchange {isProcessing ? 'In Progress' : 'Pending'}
            </span>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isAuthenticated ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className={isAuthenticated ? 'text-green-600' : 'text-gray-500'}>
              Authentication {isAuthenticated ? 'Complete' : 'Pending'}
            </span>
          </div>
        </div>

        {/* Error indicator */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-6">
            <p className="text-red-600 text-sm font-medium">
              Error: {error.message}
            </p>
          </div>
        )}

        {/* Debug information - only show in development */}
        {process.env.NODE_ENV === 'development' && debugInfo && (
          <details className="mt-6 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
              Debug Information (Development Only)
            </summary>
            <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto max-h-64">
              {debugInfo}
            </pre>
          </details>
        )}
      </div>
    </div>
  )
}
