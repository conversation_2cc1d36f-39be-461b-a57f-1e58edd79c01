'use client'

import { useEffect } from 'react'

/**
 * OIDC Silent Authentication Callback Page
 * 
 * This page handles silent token renewal in an iframe.
 * It's used by the OIDC client to refresh tokens without user interaction.
 * 
 * The URL for this page should be registered as a silent redirect URI in your OIDC provider:
 * - Development: http://localhost:3000/auth/silent-callback
 * - Production: https://your-app.com/auth/silent-callback
 * 
 * This page should be minimal and not render any visible content since it runs in an iframe.
 */
export default function SilentCallbackPage() {
  useEffect(() => {
    // The react-oidc-context library automatically handles the silent callback
    // This page just needs to exist for the iframe to load
    
    // Optional: Log that silent renewal is happening (for debugging)
    if (process.env.NODE_ENV === 'development') {
      console.log('Silent authentication callback processed')
    }
  }, [])

  // Return minimal content since this runs in an iframe
  return (
    <div style={{ display: 'none' }}>
      Silent authentication callback
    </div>
  )
}
