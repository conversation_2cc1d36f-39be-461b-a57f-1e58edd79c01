"use client"

import { useState, useEffect, useRef } from "react"
import { useChat } from "@ai-sdk/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { ChatSidebar } from "@/components/chat-sidebar"
import { ChatHeader } from "@/components/chat-header"
import { AuthDialog } from "@/components/auth-dialog"
import { useOidcAuth } from "@/hooks/use-oidc-auth"
import { Send, Bot, User, AlertCircle } from "lucide-react"

export default function ChatBot() {
  const auth = useOidcAuth()
  const [currentChatId, setCurrentChatId] = useState<string>()
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Get authentication state from OIDC
  const isLoggedIn = auth.isAuthenticated

  // Handle authentication errors
  useEffect(() => {
    if (auth.error) {
      console.error('OIDC Authentication Error:', auth.error)
      // You can add user-friendly error notifications here
    }
  }, [auth.error])

  // Custom chat hook with authentication handling
  const { messages, input, handleInputChange, handleSubmit, isLoading, setMessages } = useChat({
    body: {
      isLoggedIn,
    },
    onResponse: async (response) => {
      if (!isLoggedIn && response.ok) {
        // Handle non-streaming response for non-logged-in users
        const data = await response.json()
        if (data.message) {
          // Add the login prompt message to the chat
          setMessages((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              role: "assistant" as const,
              parts: [{ type: "text" as const, text: data.message }],
            },
          ])
        }
      }
    },
  })

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const handleNewChat = () => {
    setMessages([])
    setCurrentChatId(undefined)
  }

  const handleSelectChat = (chatId: string) => {
    if (!isLoggedIn) return
    setCurrentChatId(chatId)
    setMessages([])
  }

  const handleLogin = () => {
    setAuthDialogOpen(true)
  }

  const handleLogout = async () => {
    try {
      await auth.signOut()
      // Clear local state
      setMessages([])
      setCurrentChatId(undefined)
    } catch (error) {
      console.error('Logout error:', error)
      // Fallback: clear local state even if OIDC logout fails
      setMessages([])
      setCurrentChatId(undefined)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <SidebarProvider>
        <ChatSidebar
          onNewChat={handleNewChat}
          currentChatId={currentChatId}
          onSelectChat={handleSelectChat}
          isLoggedIn={isLoggedIn}
        />
        <SidebarInset>
          <ChatHeader isLoggedIn={isLoggedIn} onLogin={handleLogin} onLogout={handleLogout} />

          <main className="flex-1 p-4">
            <Card className="h-[calc(100vh-8rem)] flex flex-col shadow-xl">
              <CardContent className="flex-1 p-0">
                <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
                  {messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-center">
                      <Bot className="h-16 w-16 text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold text-gray-600 mb-2">Welcome to AI Chatbot</h3>
                      <p className="text-gray-500 max-w-md">
                        {isLoggedIn
                          ? "Start a new conversation or select from your chat history."
                          : "You can start chatting right away! For full features and to save your chat history, please log in."}
                      </p>
                      {!isLoggedIn && (
                        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                          <div className="flex items-center gap-2 text-amber-800">
                            <AlertCircle className="h-4 w-4" />
                            <span className="text-sm">Limited features without login</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}
                        >
                          {message.role === "assistant" && (
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-blue-100">
                                <Bot className="h-4 w-4 text-blue-600" />
                              </AvatarFallback>
                            </Avatar>
                          )}

                          <div
                            className={`max-w-[70%] rounded-lg px-4 py-2 ${
                              message.role === "user" ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-900"
                            }`}
                          >
                            {message.parts.map((part, i) => {
                              switch (part.type) {
                                case "text":
                                  return (
                                    <div key={`${message.id}-${i}`} className="whitespace-pre-wrap">
                                      {part.text}
                                    </div>
                                  )
                              }
                            })}
                          </div>

                          {message.role === "user" && (
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-gray-100">
                                <User className="h-4 w-4 text-gray-600" />
                              </AvatarFallback>
                            </Avatar>
                          )}
                        </div>
                      ))}

                      {isLoading && (
                        <div className="flex gap-3 justify-start">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="bg-blue-100">
                              <Bot className="h-4 w-4 text-blue-600" />
                            </AvatarFallback>
                          </Avatar>
                          <div className="bg-gray-100 rounded-lg px-4 py-2">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                              <div
                                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                style={{ animationDelay: "0.1s" }}
                              ></div>
                              <div
                                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                style={{ animationDelay: "0.2s" }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>

              <CardFooter className="border-t bg-white/50 backdrop-blur-sm p-4">
                <form onSubmit={handleSubmit} className="flex w-full gap-2">
                  <Input
                    value={input}
                    onChange={handleInputChange}
                    placeholder="Type your message here..."
                    className="flex-1"
                    autoFocus
                  />
                  <Button type="submit" disabled={isLoading || !input.trim()} size="icon" className="shrink-0">
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              </CardFooter>
            </Card>
          </main>
        </SidebarInset>
      </SidebarProvider>

      <AuthDialog
        open={authDialogOpen}
        onOpenChange={setAuthDialogOpen}
      />
    </div>
  )
}
