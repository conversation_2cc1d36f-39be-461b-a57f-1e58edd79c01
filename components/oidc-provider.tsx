'use client'

import React, { createContext, useContext } from 'react'
import { AuthProvider, useAuth } from 'react-oidc-context'
import { oidcConfig, isOidcConfigured } from '@/lib/oidc-config'

// Mock auth context for when OIDC is not configured
const MockAuthContext = createContext({
  isAuthenticated: false,
  isLoading: false,
  error: null,
  user: null,
  signinRedirect: async () => {
    console.warn('OIDC not configured. Please configure your OIDC settings.')
  },
  signoutRedirect: async () => {
    console.warn('OIDC not configured.')
  },
})

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <MockAuthContext.Provider value={{
      isAuthenticated: false,
      isLoading: false,
      error: null,
      user: null,
      signinRedirect: async () => {
        console.warn('OIDC not configured. Please set your environment variables.')
      },
      signoutRedirect: async () => {
        console.warn('OIDC not configured.')
      },
    }}>
      {children}
    </MockAuthContext.Provider>
  )
}

interface OidcProviderProps {
  children: React.ReactNode
}

/**
 * OIDC Authentication Provider
 * 
 * This component wraps the application with OIDC authentication context.
 * It provides authentication state and methods throughout the component tree.
 * 
 * Features:
 * - Automatic token management and renewal
 * - Silent authentication for seamless user experience
 * - Proper error handling for authentication failures
 * - Integration with your SSO identity server
 */
export function OidcProvider({ children }: OidcProviderProps) {
  // If OIDC is not configured, use mock provider
  if (!isOidcConfigured()) {
    console.warn('OIDC not configured. Please set NEXT_PUBLIC_OIDC_AUTHORITY and NEXT_PUBLIC_OIDC_CLIENT_ID environment variables.')
    return <MockAuthProvider>{children}</MockAuthProvider>
  }

  // Handle OIDC events
  const onSigninCallback = () => {
    // Called after successful sign-in
    // Remove any query parameters from URL after authentication
    window.history.replaceState(
      {},
      document.title,
      window.location.pathname
    )
  }

  const onSignoutCallback = () => {
    // Called after successful sign-out
    // Clean up any application state if needed
    console.log('User signed out successfully')
  }

  const onSigninError = (error: Error) => {
    // Handle sign-in errors
    console.error('OIDC Sign-in Error:', error)
    
    // You can add custom error handling here, such as:
    // - Showing user-friendly error messages
    // - Logging errors to your monitoring service
    // - Redirecting to an error page
  }

  const onSignoutError = (error: Error) => {
    // Handle sign-out errors
    console.error('OIDC Sign-out Error:', error)
  }

  const onUserLoaded = (user: any) => {
    // Called when user profile is loaded
    console.log('User loaded:', user.profile)
    
    // You can add custom logic here, such as:
    // - Storing user preferences
    // - Initializing user-specific data
    // - Analytics tracking
  }

  const onUserUnloaded = () => {
    // Called when user is unloaded (signed out)
    console.log('User unloaded')
    
    // Clean up user-specific data
  }

  const onAccessTokenExpiring = () => {
    // Called when access token is about to expire
    console.log('Access token expiring, attempting silent renewal...')
  }

  const onAccessTokenExpired = () => {
    // Called when access token has expired
    console.log('Access token expired')
  }

  const onSilentRenewError = (error: Error) => {
    // Called when silent token renewal fails
    console.error('Silent token renewal failed:', error)
    
    // You might want to redirect to login or show a notification
    // that the user needs to re-authenticate
  }

  return (
    <AuthProvider
      {...oidcConfig}
      onSigninCallback={onSigninCallback}
      onSignoutCallback={onSignoutCallback}
      onSigninError={onSigninError}
      onSignoutError={onSignoutError}
      onUserLoaded={onUserLoaded}
      onUserUnloaded={onUserUnloaded}
      onAccessTokenExpiring={onAccessTokenExpiring}
      onAccessTokenExpired={onAccessTokenExpired}
      onSilentRenewError={onSilentRenewError}
    >
      {children}
    </AuthProvider>
  )
}

/**
 * Hook to access OIDC authentication state and methods
 *
 * This hook works whether OIDC is configured or not.
 * When OIDC is not configured, it returns a mock auth object.
 *
 * Usage:
 * ```tsx
 * import { useOidcAuth } from '@/components/oidc-provider'
 *
 * function MyComponent() {
 *   const auth = useOidcAuth()
 *
 *   if (auth.isLoading) return <div>Loading...</div>
 *   if (auth.error) return <div>Error: {auth.error.message}</div>
 *   if (!auth.isAuthenticated) return <div>Not authenticated</div>
 *
 *   return <div>Welcome, {auth.user?.profile.name}!</div>
 * }
 * ```
 */
export function useOidcAuth() {
  if (!isOidcConfigured()) {
    return useContext(MockAuthContext)
  }
  return useAuth()
}
