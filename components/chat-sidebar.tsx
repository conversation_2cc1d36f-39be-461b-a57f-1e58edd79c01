"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { MessageSquare, Plus, Trash2, Lock } from "lucide-react"

// Mock chat history data
const mockChatHistory = [
  {
    id: "1",
    title: "How to learn React?",
    lastMessage: "Thanks for the React learning tips!",
    timestamp: "2 hours ago",
  },
  {
    id: "2",
    title: "JavaScript best practices",
    lastMessage: "What about async/await patterns?",
    timestamp: "1 day ago",
  },
  {
    id: "3",
    title: "API integration help",
    lastMessage: "Perfect, that solved my issue!",
    timestamp: "3 days ago",
  },
  {
    id: "4",
    title: "CSS Grid vs Flexbox",
    lastMessage: "When should I use Grid over Flexbox?",
    timestamp: "1 week ago",
  },
  {
    id: "5",
    title: "Database design question",
    lastMessage: "Thanks for the normalization explanation",
    timestamp: "2 weeks ago",
  },
]

interface ChatSidebarProps {
  onNewChat: () => void
  currentChatId?: string
  onSelectChat: (chatId: string) => void
  isLoggedIn: boolean
}

export function ChatSidebar({ onNewChat, currentChatId, onSelectChat, isLoggedIn }: ChatSidebarProps) {
  const [chatHistory, setChatHistory] = useState(mockChatHistory)

  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (!isLoggedIn) return
    setChatHistory((prev) => prev.filter((chat) => chat.id !== chatId))
  }

  return (
    <Sidebar className="border-r">
      <SidebarHeader className="p-4">
        <Button onClick={onNewChat} className="w-full justify-start gap-2">
          <Plus className="h-4 w-4" />
          New Chat
        </Button>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2">
            Recent Chats
            {!isLoggedIn && <Lock className="h-3 w-3 text-muted-foreground" />}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            {!isLoggedIn ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                <Lock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Log in to access your chat history</p>
              </div>
            ) : (
              <ScrollArea className="h-[calc(100vh-12rem)]">
                <SidebarMenu>
                  {chatHistory.map((chat) => (
                    <SidebarMenuItem key={chat.id}>
                      <SidebarMenuButton
                        asChild
                        isActive={currentChatId === chat.id}
                        className="h-auto p-3 justify-start group"
                      >
                        <div className="cursor-pointer w-full" onClick={() => onSelectChat(chat.id)}>
                          <div className="flex items-start gap-2 w-full">
                            <MessageSquare className="h-4 w-4 mt-0.5 shrink-0" />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm truncate">{chat.title}</div>
                              <div className="text-xs text-muted-foreground truncate">{chat.lastMessage}</div>
                              <div className="text-xs text-muted-foreground mt-1">{chat.timestamp}</div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => handleDeleteChat(chat.id, e)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </ScrollArea>
            )}
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarRail />
    </Sidebar>
  )
}
