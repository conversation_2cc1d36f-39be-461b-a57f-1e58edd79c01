"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useOidcAuth } from "@/hooks/use-oidc-auth"
import { LogIn, AlertCircle } from "lucide-react"
import { useState } from "react"

interface AuthDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AuthDialog({ open, onOpenChange }: AuthDialogProps) {
  const auth = useOidcAuth()
  const [isLoggingIn, setIsLoggingIn] = useState(false)

  const handleLogin = async () => {
    try {
      setIsLoggingIn(true)
      await auth.signIn()
      // The user will be redirected to the OIDC provider
      // After successful authentication, they'll be redirected back to /auth/callback
    } catch (error) {
      console.error('Login error:', error)
      setIsLoggingIn(false)
      // Error handling is done in the OIDC client
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Sign In</DialogTitle>
          <DialogDescription>
            Sign in with your SSO account to access the AI chatbot and save your chat history.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {auth.error && (
            <div className="flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span>
                Authentication failed. Please try again or contact support if the problem persists.
              </span>
            </div>
          )}

          <Button
            onClick={handleLogin}
            disabled={isLoggingIn || auth.isLoading}
            className="w-full"
          >
            <LogIn className="h-4 w-4 mr-2" />
            {isLoggingIn || auth.isLoading ? 'Signing In...' : 'Sign In with SSO'}
          </Button>

          <div className="text-center text-sm text-muted-foreground">
            <p>
              You will be redirected to your organization's sign-in page to authenticate securely.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
