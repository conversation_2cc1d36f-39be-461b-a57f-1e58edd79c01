#!/usr/bin/env node

/**
 * OIDC Discovery Test Script
 * 
 * This script tests your OIDC provider's discovery endpoint
 * Run with: node scripts/test-oidc-discovery.js
 */

const https = require('https')
const http = require('http')

// Get authority from environment or command line
const authority = process.env.NEXT_PUBLIC_OIDC_AUTHORITY || process.argv[2]

if (!authority) {
  console.error('❌ No OIDC authority provided')
  console.log('Usage: node scripts/test-oidc-discovery.js [authority-url]')
  console.log('Or set NEXT_PUBLIC_OIDC_AUTHORITY environment variable')
  process.exit(1)
}

console.log(`🔍 Testing OIDC discovery for: ${authority}`)

const discoveryUrl = `${authority}/.well-known/openid-configuration`
console.log(`📡 Discovery URL: ${discoveryUrl}`)

const client = discoveryUrl.startsWith('https:') ? https : http

const request = client.get(discoveryUrl, (response) => {
  let data = ''
  
  response.on('data', (chunk) => {
    data += chunk
  })
  
  response.on('end', () => {
    console.log(`📊 Response Status: ${response.statusCode}`)
    console.log(`📋 Response Headers:`)
    Object.entries(response.headers).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`)
    })
    
    if (response.statusCode === 200) {
      try {
        const config = JSON.parse(data)
        console.log('\n✅ Discovery successful!')
        console.log('\n📄 OIDC Configuration:')
        console.log(`   Issuer: ${config.issuer}`)
        console.log(`   Authorization Endpoint: ${config.authorization_endpoint}`)
        console.log(`   Token Endpoint: ${config.token_endpoint}`)
        console.log(`   UserInfo Endpoint: ${config.userinfo_endpoint}`)
        console.log(`   JWKS URI: ${config.jwks_uri}`)
        console.log(`   End Session Endpoint: ${config.end_session_endpoint || 'Not provided'}`)
        
        console.log('\n🔧 Supported Features:')
        console.log(`   Response Types: ${(config.response_types_supported || []).join(', ')}`)
        console.log(`   Grant Types: ${(config.grant_types_supported || []).join(', ')}`)
        console.log(`   Scopes: ${(config.scopes_supported || []).join(', ')}`)
        console.log(`   PKCE Methods: ${(config.code_challenge_methods_supported || []).join(', ')}`)
        
        // Check for common requirements
        console.log('\n🔍 Compatibility Check:')
        
        const hasAuthCode = (config.response_types_supported || []).includes('code')
        console.log(`   Authorization Code Flow: ${hasAuthCode ? '✅' : '❌'}`)
        
        const hasPKCE = (config.code_challenge_methods_supported || []).includes('S256')
        console.log(`   PKCE Support: ${hasPKCE ? '✅' : '❌'}`)
        
        const hasOpenId = (config.scopes_supported || []).includes('openid')
        console.log(`   OpenID Scope: ${hasOpenId ? '✅' : '❌'}`)
        
        const hasProfile = (config.scopes_supported || []).includes('profile')
        console.log(`   Profile Scope: ${hasProfile ? '✅' : '⚠️ Optional'}`)
        
        const hasEmail = (config.scopes_supported || []).includes('email')
        console.log(`   Email Scope: ${hasEmail ? '✅' : '⚠️ Optional'}`)
        
        if (!hasAuthCode || !hasPKCE || !hasOpenId) {
          console.log('\n⚠️  Warning: Some required features may not be supported')
        } else {
          console.log('\n🎉 Your OIDC provider appears to be compatible!')
        }
        
      } catch (error) {
        console.error('\n❌ Failed to parse discovery response:')
        console.error(error.message)
        console.log('\n📄 Raw response:')
        console.log(data)
      }
    } else {
      console.error(`\n❌ Discovery failed with status ${response.statusCode}`)
      console.log('\n📄 Response body:')
      console.log(data)
    }
  })
})

request.on('error', (error) => {
  console.error('\n❌ Request failed:')
  console.error(error.message)
  
  if (error.code === 'ENOTFOUND') {
    console.log('\n💡 Possible issues:')
    console.log('   - Check if the authority URL is correct')
    console.log('   - Verify network connectivity')
    console.log('   - Ensure the OIDC provider is accessible')
  } else if (error.code === 'ECONNREFUSED') {
    console.log('\n💡 Possible issues:')
    console.log('   - OIDC provider may be down')
    console.log('   - Port may be incorrect')
    console.log('   - Firewall may be blocking the connection')
  }
})

request.setTimeout(10000, () => {
  console.error('\n❌ Request timed out after 10 seconds')
  request.destroy()
})
