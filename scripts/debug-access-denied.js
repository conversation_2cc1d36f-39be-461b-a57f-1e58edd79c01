#!/usr/bin/env node

/**
 * Access Denied Debug Script
 * 
 * This script helps debug access_denied errors by testing your OIDC configuration
 * Run with: node scripts/debug-access-denied.js
 */

const https = require('https')
const http = require('http')
const crypto = require('crypto')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const config = {
  authority: process.env.NEXT_PUBLIC_OIDC_AUTHORITY,
  client_id: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID,
  redirect_uri: process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI,
  scope: process.env.NEXT_PUBLIC_OIDC_SCOPE || 'openid profile email'
}

console.log('🔍 OIDC Access Denied Debug Tool')
console.log('================================\n')

// Validate configuration
console.log('📋 Current Configuration:')
Object.entries(config).forEach(([key, value]) => {
  console.log(`   ${key}: ${value || '(not set)'}`)
})
console.log()

if (!config.authority || !config.client_id) {
  console.error('❌ Missing required configuration. Please check your .env.local file.')
  process.exit(1)
}

async function fetchJson(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http
    const request = client.get(url, (response) => {
      let data = ''
      response.on('data', chunk => data += chunk)
      response.on('end', () => {
        try {
          if (response.statusCode === 200) {
            resolve(JSON.parse(data))
          } else {
            reject(new Error(`HTTP ${response.statusCode}: ${data}`))
          }
        } catch (error) {
          reject(error)
        }
      })
    })
    request.on('error', reject)
    request.setTimeout(10000, () => {
      request.destroy()
      reject(new Error('Request timeout'))
    })
  })
}

async function testDiscovery() {
  console.log('🔍 Testing OIDC Discovery...')
  try {
    const discoveryUrl = `${config.authority}/.well-known/openid-configuration`
    const discovery = await fetchJson(discoveryUrl)
    
    console.log('✅ Discovery successful!')
    console.log(`   Issuer: ${discovery.issuer}`)
    console.log(`   Authorization Endpoint: ${discovery.authorization_endpoint}`)
    console.log(`   Token Endpoint: ${discovery.token_endpoint}`)
    console.log(`   Supported Scopes: ${discovery.scopes_supported?.join(', ') || 'Not specified'}`)
    console.log(`   PKCE Methods: ${discovery.code_challenge_methods_supported?.join(', ') || 'Not specified'}`)
    console.log(`   Grant Types: ${discovery.grant_types_supported?.join(', ') || 'Not specified'}`)
    console.log()
    
    return discovery
  } catch (error) {
    console.error('❌ Discovery failed:', error.message)
    return null
  }
}

function generateAuthUrl(discovery) {
  console.log('🔗 Generating Authorization URL...')
  
  // Generate PKCE parameters
  const codeVerifier = crypto.randomBytes(32).toString('base64url')
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url')
  const state = crypto.randomBytes(16).toString('hex')
  const nonce = crypto.randomBytes(16).toString('hex')
  
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: config.client_id,
    redirect_uri: config.redirect_uri,
    scope: config.scope,
    state: state,
    nonce: nonce,
    code_challenge: codeChallenge,
    code_challenge_method: 'S256'
  })
  
  const authUrl = `${discovery.authorization_endpoint}?${params.toString()}`
  
  console.log('✅ Authorization URL generated!')
  console.log(`   URL: ${authUrl}`)
  console.log(`   State: ${state}`)
  console.log(`   Code Challenge: ${codeChallenge}`)
  console.log(`   Code Verifier: ${codeVerifier}`)
  console.log()
  
  return { authUrl, state, codeVerifier, codeChallenge }
}

function analyzeConfiguration(discovery) {
  console.log('🔍 Configuration Analysis:')
  
  const issues = []
  const warnings = []
  
  // Check redirect URI
  if (!config.redirect_uri) {
    issues.push('Redirect URI not configured')
  } else if (!config.redirect_uri.includes('/auth/callback')) {
    warnings.push('Redirect URI should end with /auth/callback')
  }
  
  // Check scopes
  const requestedScopes = config.scope.split(' ')
  const supportedScopes = discovery.scopes_supported || []
  
  requestedScopes.forEach(scope => {
    if (!supportedScopes.includes(scope)) {
      warnings.push(`Scope "${scope}" may not be supported by the provider`)
    }
  })
  
  // Check PKCE support
  const pkceMethods = discovery.code_challenge_methods_supported || []
  if (!pkceMethods.includes('S256')) {
    issues.push('Provider does not support PKCE with S256 method')
  }
  
  // Check grant types
  const grantTypes = discovery.grant_types_supported || []
  if (!grantTypes.includes('authorization_code')) {
    issues.push('Provider does not support authorization_code grant type')
  }
  
  if (issues.length > 0) {
    console.log('❌ Critical Issues:')
    issues.forEach(issue => console.log(`   • ${issue}`))
  }
  
  if (warnings.length > 0) {
    console.log('⚠️  Warnings:')
    warnings.forEach(warning => console.log(`   • ${warning}`))
  }
  
  if (issues.length === 0 && warnings.length === 0) {
    console.log('✅ Configuration looks good!')
  }
  
  console.log()
}

function printTroubleshootingSteps() {
  console.log('🛠️  Troubleshooting Steps for access_denied:')
  console.log()
  
  console.log('1. 🔧 Check Client Configuration in SSO Provider:')
  console.log(`   • Client ID: ${config.client_id}`)
  console.log(`   • Client Type: Public / SPA`)
  console.log(`   • Grant Types: authorization_code`)
  console.log(`   • Response Types: code`)
  console.log(`   • Redirect URIs: ${config.redirect_uri}`)
  console.log(`   • PKCE: Required/Enabled`)
  console.log(`   • Scopes: ${config.scope}`)
  console.log()
  
  console.log('2. 🔍 Verify Exact Settings:')
  console.log('   • Redirect URI must match EXACTLY (no trailing slash differences)')
  console.log('   • Client must be enabled/active')
  console.log('   • User must have permission to access this client')
  console.log('   • Requested scopes must be allowed for this client')
  console.log()
  
  console.log('3. 🧪 Quick Tests:')
  console.log('   • Try with minimal scope: NEXT_PUBLIC_OIDC_SCOPE=openid')
  console.log('   • Check SSO provider logs for detailed error messages')
  console.log('   • Test with a different user account')
  console.log('   • Verify client secret is NOT set (for public clients)')
  console.log()
  
  console.log('4. 📞 Contact SSO Administrator:')
  console.log('   • Ask them to verify client configuration')
  console.log('   • Request access to detailed error logs')
  console.log('   • Confirm user permissions for this application')
  console.log()
}

async function main() {
  const discovery = await testDiscovery()
  
  if (!discovery) {
    console.log('Cannot proceed without discovery document.')
    return
  }
  
  analyzeConfiguration(discovery)
  
  const authData = generateAuthUrl(discovery)
  
  console.log('🌐 Manual Test Instructions:')
  console.log('1. Copy the authorization URL above')
  console.log('2. Open it in your browser')
  console.log('3. Complete the login process')
  console.log('4. Check the callback URL for error parameters')
  console.log('5. If you get access_denied, check the troubleshooting steps below')
  console.log()
  
  printTroubleshootingSteps()
  
  console.log('📊 For detailed analysis, visit:')
  console.log('   • http://localhost:3000/oidc-diagnostics')
  console.log('   • http://localhost:3000/access-denied-help')
}

main().catch(error => {
  console.error('❌ Script failed:', error.message)
  process.exit(1)
})
