'use client'

import { User<PERSON>anager, User, WebStorageStateStore, Log } from 'oidc-client-ts'
import { oidcConfig } from './oidc-config'

/**
 * Direct OIDC Client Implementation
 * 
 * This provides more control over the authentication flow compared to react-oidc-context
 * and allows for better debugging of token exchange issues.
 */

// Enable detailed logging in development
if (process.env.NODE_ENV === 'development') {
  Log.setLogger(console)
  Log.setLevel(Log.DEBUG)
}

// Create UserManager instance
let userManager: UserManager | null = null

export function getUserManager(): UserManager {
  if (!userManager) {
    // Enhanced configuration with better error handling
    const config = {
      ...oidcConfig,
      // Use session storage for better debugging
      userStore: new WebStorageStateStore({ store: window.sessionStorage }),
      // Additional debugging options
      stateStore: new WebStorageStateStore({ store: window.sessionStorage }),
      // Ensure we handle token exchange properly
      response_mode: 'query',
      // Add extra logging
      extraQueryParams: {
        ...oidcConfig.extraQueryParams,
      }
    }
    
    console.log('Creating UserManager with config:', config)
    userManager = new UserManager(config)
    
    // Add event listeners for debugging
    userManager.events.addUserLoaded((user: User) => {
      console.log('✅ User loaded successfully:', {
        profile: user.profile,
        access_token: user.access_token ? '[PRESENT]' : null,
        id_token: user.id_token ? '[PRESENT]' : null,
        expires_at: user.expires_at,
        expires_in: user.expires_in
      })
    })
    
    userManager.events.addUserUnloaded(() => {
      console.log('🔄 User unloaded')
    })
    
    userManager.events.addAccessTokenExpiring(() => {
      console.log('⚠️ Access token expiring')
    })
    
    userManager.events.addAccessTokenExpired(() => {
      console.log('❌ Access token expired')
    })
    
    userManager.events.addSilentRenewError((error: Error) => {
      console.error('❌ Silent renew error:', error)
    })
    
    userManager.events.addUserSignedOut(() => {
      console.log('👋 User signed out')
    })
  }
  
  return userManager
}

/**
 * Authentication Functions
 */

export async function signIn(): Promise<void> {
  console.log('🔐 Starting sign-in process...')
  try {
    const manager = getUserManager()
    await manager.signinRedirect()
    console.log('🔄 Redirecting to OIDC provider...')
  } catch (error) {
    console.error('❌ Sign-in failed:', error)
    throw error
  }
}

export async function handleSignInCallback(): Promise<User | null> {
  console.log('🔄 Processing sign-in callback...')
  try {
    const manager = getUserManager()
    
    // Get the current URL parameters for debugging
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const error = urlParams.get('error')
    const state = urlParams.get('state')
    
    console.log('📋 Callback parameters:', {
      code: code ? `${code.substring(0, 10)}...` : null,
      error,
      state,
      full_url: window.location.href
    })
    
    if (error) {
      throw new Error(`OIDC callback error: ${error}`)
    }
    
    if (!code) {
      throw new Error('No authorization code received in callback')
    }
    
    console.log('🔄 Exchanging authorization code for tokens...')
    
    // This is where the token exchange happens
    const user = await manager.signinRedirectCallback()
    
    console.log('✅ Token exchange successful!', {
      user_id: user.profile?.sub,
      name: user.profile?.name,
      email: user.profile?.email,
      access_token: user.access_token ? '[PRESENT]' : null,
      id_token: user.id_token ? '[PRESENT]' : null,
      expires_at: user.expires_at,
      expires_in: user.expires_in,
      token_type: user.token_type,
      scope: user.scope
    })
    
    return user
  } catch (error) {
    console.error('❌ Sign-in callback failed:', error)
    
    // Enhanced error logging
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      })
      
      // Check for specific token exchange errors
      if (error.message.includes('token')) {
        console.error('🔍 This appears to be a token exchange error. Check:')
        console.error('  - Client configuration in SSO provider')
        console.error('  - Token endpoint authentication method')
        console.error('  - PKCE configuration')
        console.error('  - Client secret requirements')
      }
    }
    
    throw error
  }
}

export async function signOut(): Promise<void> {
  console.log('👋 Starting sign-out process...')
  try {
    const manager = getUserManager()
    await manager.signoutRedirect()
    console.log('🔄 Redirecting to sign-out endpoint...')
  } catch (error) {
    console.error('❌ Sign-out failed:', error)
    throw error
  }
}

export async function getUser(): Promise<User | null> {
  try {
    const manager = getUserManager()
    const user = await manager.getUser()
    
    if (user && !user.expired) {
      console.log('✅ Valid user session found:', {
        user_id: user.profile?.sub,
        expires_at: user.expires_at,
        expires_in: user.expires_in
      })
      return user
    } else if (user && user.expired) {
      console.log('⚠️ User session expired')
      return null
    } else {
      console.log('ℹ️ No user session found')
      return null
    }
  } catch (error) {
    console.error('❌ Failed to get user:', error)
    return null
  }
}

export async function renewToken(): Promise<User | null> {
  console.log('🔄 Attempting token renewal...')
  try {
    const manager = getUserManager()
    const user = await manager.signinSilent()
    console.log('✅ Token renewal successful')
    return user
  } catch (error) {
    console.error('❌ Token renewal failed:', error)
    throw error
  }
}

export async function removeUser(): Promise<void> {
  console.log('🗑️ Removing user session...')
  try {
    const manager = getUserManager()
    await manager.removeUser()
    console.log('✅ User session removed')
  } catch (error) {
    console.error('❌ Failed to remove user:', error)
    throw error
  }
}

/**
 * Utility Functions
 */

export function isAuthenticated(user: User | null): boolean {
  return !!(user && !user.expired)
}

export function getAccessToken(user: User | null): string | null {
  return user?.access_token || null
}

export function getUserProfile(user: User | null): any {
  return user?.profile || null
}
