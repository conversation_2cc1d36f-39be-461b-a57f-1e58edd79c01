import { UserManagerSettings } from 'oidc-client-ts'

/**
 * OIDC Configuration for authentication with your SSO identity server
 * 
 * Fill in the following configuration values with your actual OIDC provider settings:
 * 
 * 1. authority: The base URL of your OIDC provider (issuer URL)
 *    Example: "https://your-identity-server.com" or "https://auth.your-domain.com"
 * 
 * 2. client_id: The client ID registered with your OIDC provider
 *    Example: "chatbot-frontend-client"
 * 
 * 3. client_secret: The client secret for confidential clients (if required)
 *    Example: "your-client-secret" (only for confidential clients)
 * 
 * 4. redirect_uri: The URL where users will be redirected after successful authentication
 *    Should match your application's domain + "/auth/callback"
 *    Example: "http://localhost:3000/auth/callback" (development)
 *             "https://your-app.com/auth/callback" (production)
 * 
 * 5. post_logout_redirect_uri: Where users are redirected after logout
 *    Example: "http://localhost:3000" (development)
 *             "https://your-app.com" (production)
 * 
 * 6. silent_redirect_uri: For silent token renewal (optional but recommended)
 *    Example: "http://localhost:3000/auth/silent-callback" (development)
 *             "https://your-app.com/auth/silent-callback" (production)
 */

// Check if OIDC is configured
export const isOidcConfigured = () => {
  return !!(process.env.NEXT_PUBLIC_OIDC_AUTHORITY && process.env.NEXT_PUBLIC_OIDC_CLIENT_ID)
}

export const oidcConfig: UserManagerSettings = {
  // REQUIRED: Your OIDC provider's issuer URL
  authority: process.env.NEXT_PUBLIC_OIDC_AUTHORITY || 'https://placeholder.example.com',

  // REQUIRED: Client ID registered with your OIDC provider
  client_id: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID || 'placeholder-client-id',
  
  // OPTIONAL: Client Secret for confidential clients
  // Include this if your OIDC client is configured as confidential
  client_secret: process.env.NEXT_PUBLIC_OIDC_CLIENT_SECRET,
  
  // REQUIRED: Redirect URI after successful authentication
  redirect_uri: process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI || 
    (typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : ''),
  
  // REQUIRED: Redirect URI after logout
  post_logout_redirect_uri: process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI || 
    (typeof window !== 'undefined' ? window.location.origin : ''),
  
  // OPTIONAL: Silent redirect URI for token renewal
  silent_redirect_uri: process.env.NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI || 
    (typeof window !== 'undefined' ? `${window.location.origin}/auth/silent-callback` : ''),
  
  // Response type - use 'code' for authorization code flow
  // For confidential clients with client_secret, this should be 'code'
  response_type: 'code',
  
  // Scopes to request from the OIDC provider
  // Modify these based on what your identity server supports
  // Can be overridden with NEXT_PUBLIC_OIDC_SCOPE for debugging
  scope: process.env.NEXT_PUBLIC_OIDC_SCOPE || 'openid profile email',
  
  // Automatically attempt to renew tokens before they expire
  // Disable in development if causing issues
  automaticSilentRenew: process.env.NEXT_PUBLIC_DISABLE_SILENT_RENEWAL === 'true' ? false : true,

  // Include ID token in silent renew
  includeIdTokenInSilentRenew: true,

  // Load user profile from user info endpoint
  loadUserInfo: true,

  // Filter protocol claims from profile
  filterProtocolClaims: true,

  // Validate sub claim on token renewal
  validateSubOnSilentRenew: true,

  // Token renewal time before expiry (in seconds)
  accessTokenExpiringNotificationTimeInSeconds: 60,

  // Silent renew timeout (in milliseconds)
  silentRequestTimeoutInSeconds: 10,
  // Check session interval (in milliseconds) - disable during debugging
  checkSessionIntervalInSeconds: process.env.NODE_ENV === 'development' ? undefined : 2,

  // Disable monitoring session state during development to avoid interference
  monitorSession: process.env.NODE_ENV !== 'development',
  // Query session status
  query_status_response_type: 'code',
  
  // Additional query parameters for authorization requests (optional)
  // extraQueryParams: {
  //   // Add any custom parameters your OIDC provider requires
  //   // Example: 'acr_values': 'tenant:your-tenant-id'
  // },
  
  // Additional token request parameters (optional)
  // extraTokenParams: {
  //   // Add any custom parameters for token requests
  // },
  
  // Metadata configuration (optional - auto-discovered from /.well-known/openid-configuration)
  // Your provider supports discovery, so this is not needed
  // metadata: {
  //   // Endpoints are automatically discovered from https://sso.veasy.vn/.well-known/openid-configuration
  // },
}

/**
 * Environment Variables Setup:
 * 
 * Create a .env.local file in your project root with the following variables:
 * 
 * NEXT_PUBLIC_OIDC_AUTHORITY=https://your-identity-server.com
 * NEXT_PUBLIC_OIDC_CLIENT_ID=your-client-id
 * NEXT_PUBLIC_OIDC_CLIENT_SECRET=your-client-secret (for confidential clients)
 * NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback
 * NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000
 * NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback
 * 
 * For production, update these URLs to match your production domain.
 */

/**
 * OIDC Provider Registration:
 * 
 * When registering your application with your OIDC provider, make sure to:
 * 
 * 1. Set the correct redirect URIs:
 *    - http://localhost:3000/auth/callback (development)
 *    - https://your-app.com/auth/callback (production)
 *    - http://localhost:3000/auth/silent-callback (development - for silent renewal)
 *    - https://your-app.com/auth/silent-callback (production - for silent renewal)
 * 
 * 2. Set the correct post-logout redirect URIs:
 *    - http://localhost:3000 (development)
 *    - https://your-app.com (production)
 * 
 * 3. Configure client type:
 *    - Public client: Use PKCE, no client secret required
 *    - Confidential client: Requires client secret
 * 
 * 4. Enable the authorization code flow
 * 5. Configure the appropriate scopes (openid, profile, email)
 * 6. Set token lifetimes according to your security requirements
 */
