/**
 * OIDC Configuration Diagnostics
 * 
 * This module provides diagnostic tools to help troubleshoot OIDC configuration issues.
 */

export interface OidcDiagnosticResult {
  check: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: any
}

export interface OidcDiagnosticReport {
  overall: 'pass' | 'fail' | 'warning'
  results: OidcDiagnosticResult[]
  recommendations: string[]
}

/**
 * Validate OIDC configuration and environment variables
 */
export function validateOidcConfig(): OidcDiagnosticReport {
  const results: OidcDiagnosticResult[] = []
  const recommendations: string[] = []

  // Check required environment variables
  const authority = process.env.NEXT_PUBLIC_OIDC_AUTHORITY
  const clientId = process.env.NEXT_PUBLIC_OIDC_CLIENT_ID
  const redirectUri = process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI
  const postLogoutUri = process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI

  // Authority check
  if (!authority) {
    results.push({
      check: 'OIDC Authority',
      status: 'fail',
      message: 'NEXT_PUBLIC_OIDC_AUTHORITY is not set',
    })
    recommendations.push('Set NEXT_PUBLIC_OIDC_AUTHORITY to your OIDC provider URL')
  } else if (!authority.startsWith('https://')) {
    results.push({
      check: 'OIDC Authority',
      status: 'warning',
      message: 'Authority should use HTTPS in production',
      details: { authority }
    })
  } else {
    results.push({
      check: 'OIDC Authority',
      status: 'pass',
      message: 'Authority is properly configured',
      details: { authority }
    })
  }

  // Client ID check
  if (!clientId) {
    results.push({
      check: 'Client ID',
      status: 'fail',
      message: 'NEXT_PUBLIC_OIDC_CLIENT_ID is not set',
    })
    recommendations.push('Set NEXT_PUBLIC_OIDC_CLIENT_ID to your registered client ID')
  } else {
    results.push({
      check: 'Client ID',
      status: 'pass',
      message: 'Client ID is configured',
      details: { clientId }
    })
  }

  // Redirect URI check
  if (!redirectUri) {
    results.push({
      check: 'Redirect URI',
      status: 'warning',
      message: 'NEXT_PUBLIC_OIDC_REDIRECT_URI not set, using default',
    })
    recommendations.push('Set NEXT_PUBLIC_OIDC_REDIRECT_URI explicitly for better control')
  } else if (!redirectUri.includes('/auth/callback')) {
    results.push({
      check: 'Redirect URI',
      status: 'warning',
      message: 'Redirect URI should end with /auth/callback',
      details: { redirectUri }
    })
    recommendations.push('Ensure redirect URI ends with /auth/callback')
  } else {
    results.push({
      check: 'Redirect URI',
      status: 'pass',
      message: 'Redirect URI is properly configured',
      details: { redirectUri }
    })
  }

  // Post-logout URI check
  if (!postLogoutUri) {
    results.push({
      check: 'Post-logout URI',
      status: 'warning',
      message: 'NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI not set, using default',
    })
  } else {
    results.push({
      check: 'Post-logout URI',
      status: 'pass',
      message: 'Post-logout URI is configured',
      details: { postLogoutUri }
    })
  }

  // URL consistency check
  if (redirectUri && postLogoutUri) {
    const redirectOrigin = new URL(redirectUri).origin
    const postLogoutOrigin = new URL(postLogoutUri).origin
    
    if (redirectOrigin !== postLogoutOrigin) {
      results.push({
        check: 'URL Consistency',
        status: 'warning',
        message: 'Redirect and post-logout URIs have different origins',
        details: { redirectOrigin, postLogoutOrigin }
      })
      recommendations.push('Ensure all URIs use the same origin for consistency')
    } else {
      results.push({
        check: 'URL Consistency',
        status: 'pass',
        message: 'All URIs use consistent origins',
      })
    }
  }

  // Determine overall status
  const hasFailures = results.some(r => r.status === 'fail')
  const hasWarnings = results.some(r => r.status === 'warning')
  
  const overall = hasFailures ? 'fail' : hasWarnings ? 'warning' : 'pass'

  return {
    overall,
    results,
    recommendations
  }
}

/**
 * Test OIDC provider discovery endpoint
 */
export async function testOidcDiscovery(authority: string): Promise<OidcDiagnosticResult> {
  try {
    const discoveryUrl = `${authority}/.well-known/openid-configuration`
    const response = await fetch(discoveryUrl)
    
    if (!response.ok) {
      return {
        check: 'OIDC Discovery',
        status: 'fail',
        message: `Discovery endpoint returned ${response.status}`,
        details: { discoveryUrl, status: response.status }
      }
    }

    const config = await response.json()
    
    // Check for required endpoints
    const requiredEndpoints = [
      'authorization_endpoint',
      'token_endpoint',
      'userinfo_endpoint',
      'jwks_uri'
    ]
    
    const missingEndpoints = requiredEndpoints.filter(endpoint => !config[endpoint])
    
    if (missingEndpoints.length > 0) {
      return {
        check: 'OIDC Discovery',
        status: 'warning',
        message: 'Some endpoints are missing from discovery',
        details: { missingEndpoints, config }
      }
    }

    return {
      check: 'OIDC Discovery',
      status: 'pass',
      message: 'Discovery endpoint is working correctly',
      details: { 
        discoveryUrl,
        endpoints: {
          authorization: config.authorization_endpoint,
          token: config.token_endpoint,
          userinfo: config.userinfo_endpoint,
          jwks: config.jwks_uri
        }
      }
    }
  } catch (error) {
    return {
      check: 'OIDC Discovery',
      status: 'fail',
      message: 'Failed to fetch discovery endpoint',
      details: { error: error instanceof Error ? error.message : String(error) }
    }
  }
}

/**
 * Analyze access_denied error patterns
 */
export function analyzeAccessDeniedError(callbackUrl?: string): OidcDiagnosticResult[] {
  const results: OidcDiagnosticResult[] = []

  if (callbackUrl && callbackUrl.includes('error=access_denied')) {
    const url = new URL(callbackUrl, 'http://localhost:3000')
    const error = url.searchParams.get('error')
    const errorDescription = url.searchParams.get('error_description')
    const state = url.searchParams.get('state')
    const sessionState = url.searchParams.get('session_state')

    results.push({
      check: 'Access Denied Analysis',
      status: 'fail',
      message: 'OIDC provider returned access_denied error',
      details: {
        error,
        error_description: errorDescription,
        state,
        session_state: sessionState,
        analysis: {
          likely_causes: [
            'Client not properly registered or disabled',
            'Redirect URI mismatch in SSO provider',
            'User lacks permission to access this client',
            'Requested scopes not allowed for this client',
            'Client secret required but not provided',
            'Client type misconfigured (should be Public for PKCE)'
          ],
          immediate_checks: [
            'Verify client "veasy_web_client" exists and is enabled',
            'Check redirect URI is exactly "http://localhost:3000/auth/callback"',
            'Ensure client type is set to "Public" or "SPA"',
            'Verify Authorization Code grant type is enabled',
            'Check if PKCE is required and properly configured',
            'Confirm user has access to this client application'
          ]
        }
      }
    })
  }

  return results
}

/**
 * Generate client configuration checklist
 */
export function generateClientConfigChecklist(): OidcDiagnosticResult {
  const clientId = process.env.NEXT_PUBLIC_OIDC_CLIENT_ID
  const redirectUri = process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI

  return {
    check: 'Client Configuration Checklist',
    status: 'warning',
    message: 'Verify these settings in your SSO provider admin panel',
    details: {
      client_id: clientId,
      redirect_uri: redirectUri,
      required_settings: {
        'Client ID': clientId || 'NOT_SET',
        'Client Name': 'Any descriptive name (e.g., "Chatbot Frontend")',
        'Client Type': 'Public / SPA (Single Page Application)',
        'Grant Types': ['authorization_code'],
        'Response Types': ['code'],
        'Redirect URIs': [redirectUri || 'http://localhost:3000/auth/callback'],
        'Post Logout Redirect URIs': [process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI || 'http://localhost:3000'],
        'Scopes': ['openid', 'profile', 'email'],
        'PKCE': 'Required / Enabled',
        'Client Secret': 'Not required for public clients',
        'Token Endpoint Auth Method': 'none (for public clients)',
        'Access Token Lifetime': '3600 seconds (1 hour) - recommended',
        'Refresh Token Lifetime': '2592000 seconds (30 days) - recommended'
      },
      verification_steps: [
        '1. Log into your SSO provider admin panel',
        '2. Navigate to Clients/Applications section',
        `3. Find or create client with ID: ${clientId}`,
        '4. Verify all settings match the required_settings above',
        '5. Ensure client is enabled/active',
        '6. Save changes and test authentication'
      ]
    }
  }
}

/**
 * Test specific OIDC endpoints for accessibility
 */
export async function testOidcEndpoints(authority: string): Promise<OidcDiagnosticResult[]> {
  const results: OidcDiagnosticResult[] = []

  try {
    // First get the discovery document
    const discoveryResponse = await fetch(`${authority}/.well-known/openid-configuration`)
    if (!discoveryResponse.ok) {
      results.push({
        check: 'Endpoint Testing',
        status: 'fail',
        message: 'Cannot access discovery endpoint',
        details: { status: discoveryResponse.status }
      })
      return results
    }

    const config = await discoveryResponse.json()

    // Test authorization endpoint
    try {
      const authResponse = await fetch(config.authorization_endpoint, { method: 'HEAD' })
      results.push({
        check: 'Authorization Endpoint',
        status: authResponse.ok ? 'pass' : 'warning',
        message: authResponse.ok ? 'Accessible' : `Returned ${authResponse.status}`,
        details: {
          endpoint: config.authorization_endpoint,
          status: authResponse.status,
          note: 'HEAD request to check accessibility'
        }
      })
    } catch (error) {
      results.push({
        check: 'Authorization Endpoint',
        status: 'fail',
        message: 'Cannot access authorization endpoint',
        details: {
          endpoint: config.authorization_endpoint,
          error: error instanceof Error ? error.message : String(error)
        }
      })
    }

    // Test token endpoint
    try {
      const tokenResponse = await fetch(config.token_endpoint, { method: 'HEAD' })
      results.push({
        check: 'Token Endpoint',
        status: tokenResponse.ok ? 'pass' : 'warning',
        message: tokenResponse.ok ? 'Accessible' : `Returned ${tokenResponse.status}`,
        details: {
          endpoint: config.token_endpoint,
          status: tokenResponse.status
        }
      })
    } catch (error) {
      results.push({
        check: 'Token Endpoint',
        status: 'fail',
        message: 'Cannot access token endpoint',
        details: {
          endpoint: config.token_endpoint,
          error: error instanceof Error ? error.message : String(error)
        }
      })
    }

  } catch (error) {
    results.push({
      check: 'Endpoint Testing',
      status: 'fail',
      message: 'Failed to test endpoints',
      details: { error: error instanceof Error ? error.message : String(error) }
    })
  }

  return results
}

/**
 * Generate a comprehensive diagnostic report
 */
export async function generateDiagnosticReport(): Promise<OidcDiagnosticReport> {
  const configReport = validateOidcConfig()

  // Test discovery if authority is available
  const authority = process.env.NEXT_PUBLIC_OIDC_AUTHORITY
  if (authority) {
    try {
      const discoveryResult = await testOidcDiscovery(authority)
      configReport.results.push(discoveryResult)

      if (discoveryResult.status === 'fail') {
        configReport.recommendations.push('Check if your OIDC provider is accessible and the authority URL is correct')
      } else {
        // Test individual endpoints
        const endpointResults = await testOidcEndpoints(authority)
        configReport.results.push(...endpointResults)
      }
    } catch (error) {
      configReport.results.push({
        check: 'OIDC Discovery',
        status: 'fail',
        message: 'Could not test discovery endpoint',
        details: { error: error instanceof Error ? error.message : String(error) }
      })
    }
  }

  // Add client configuration checklist
  configReport.results.push(generateClientConfigChecklist())

  // Add access denied analysis if we detect it's a common issue
  configReport.recommendations.push(
    'For access_denied errors: Verify client configuration in SSO provider admin panel',
    'Check that redirect URIs match exactly (including protocol and port)',
    'Ensure client type is set to "Public" for PKCE authentication',
    'Verify user has permission to access this client application'
  )

  // Update overall status
  const hasFailures = configReport.results.some(r => r.status === 'fail')
  const hasWarnings = configReport.results.some(r => r.status === 'warning')
  configReport.overall = hasFailures ? 'fail' : hasWarnings ? 'warning' : 'pass'

  return configReport
}

/**
 * Format diagnostic report for console output
 */
export function formatDiagnosticReport(report: OidcDiagnosticReport): string {
  let output = `\n=== OIDC Configuration Diagnostic Report ===\n`
  output += `Overall Status: ${report.overall.toUpperCase()}\n\n`

  report.results.forEach(result => {
    const status = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'
    output += `${status} ${result.check}: ${result.message}\n`
    if (result.details) {
      output += `   Details: ${JSON.stringify(result.details, null, 2)}\n`
    }
    output += '\n'
  })

  if (report.recommendations.length > 0) {
    output += `Recommendations:\n`
    report.recommendations.forEach((rec, index) => {
      output += `${index + 1}. ${rec}\n`
    })
  }

  return output
}
